{"header": {"dashboard": "Dashboard", "order": "Order", "category": "Category", "newCategory": "New Category", "categoryAddon": "Category Add-On", "product": "Product", "newProduct": "New Product", "productOption": "Product Option", "newProductOption": "New Product Option", "user": "User", "customer": "Customer", "voucher": "Voucher", "newVoucher": "New Voucher", "editVoucher": "<PERSON>", "banner": "Banner", "serviceBundle": "Service Bundle", "auditLog": "<PERSON>t Log", "topUp": "Top Up", "referralProgram": "Referral Program", "notifications": "Notifications", "setup": "Setup", "newCategoryAddon": "New Category Add-On"}, "appSidebar": {"dashboard": "Dashboard", "order": "Order", "services": "Services", "category": "Category", "categoryAddon": "Category Add-On", "product": "Product", "productOption": "Product Option", "user": "User", "customer": "Customer", "voucher": "Voucher", "banner": "Banner", "serviceBundle": "Service Bundle", "auditLog": "<PERSON>t Log", "topUp": "Top Up", "referralProgram": "Referral Program", "notifications": "Notifications", "setup": "Setup", "all": "All", "read": "Read", "noNotifications": "No notifications.", "noReadNotifications": "No read notifications.", "markAllAsRead": "Mark all as read"}, "categoryPage": {"addProduct": "Add Product", "addCategoryAddOn": "Add Category Add-On", "product": "Product", "categoryAddOn": "Category Add-On", "taskInformation": "Task Information", "details": "Details", "productVariant": "Product Variant", "addAnotherVariant": "Add <PERSON> Variant", "clearAll": "Clear All"}, "productPage": {"newProduct": "New Product", "details": "Details", "taskInformation": "Task Information", "productOption": "Product Option", "addProductOption": "Add Product Option"}, "noResults": "No Results.", "save": "Save", "addContent": "Add Content", "cancel": "Cancel", "addAnother": "Add Another", "name": "Name", "price": "Price", "status": "Status", "category": "Category", "optional": "(optional)", "title": "Title", "hours": "Hours", "bedroom": "Bedroom", "floor": "Floor", "cleaners": "Cleaners"}