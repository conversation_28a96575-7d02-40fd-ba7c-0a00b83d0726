import { useNavigate } from 'react-router';
import useCanGoBack from '@/hooks/useCanGoBack';
import { ChevronLeft } from 'lucide-react';
import { Button } from '../ui/button';
import HeaderTitle from './header-title';
import { useTranslation } from 'react-i18next';

export default function CustomHeader({ onSave }: { onSave?: () => void }) {
  const { t } = useTranslation();
  const { canGoBack } = useCanGoBack();
  const navigate = useNavigate();
  const handleBack = () => navigate(-1);

  return (
    <div className="h-[88px] p-6 pl-4 flex w-full items-center flex-row bg-background">
      <div className="flex flex-1 flex-row gap-4 items-center">
        <Button variant="ghost" size="icon" onClick={handleBack} disabled={!canGoBack}>
          <ChevronLeft className="!size-6" />
        </Button>

        <HeaderTitle />
      </div>
      <Button onClick={onSave} size="sm">
        {t('save')}
      </Button>
    </div>
  );
}
