import * as React from 'react';
import useTableState from '@/hooks/useTableState';
import TablePagination from '@/components/common/table-pagination';
import useDataTableConfig from '@/hooks/useDataTableConfig';
import DraggableContext from '@/components/common/draggable/draggable-context';
import DataTableHeader from '@/components/data-table/data-table-header';
import { Table, TableBody } from '@/components/ui/table';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableRows from '@/components/data-table/sortable-rows';
import { categoryAddOnColumns } from '@/components/data-table/category-addon-columns';
import CategoryAddonTableHeader from '@/components/category/category-addon-table-header';
import { categoryAddOnRecords } from '@/constants/data-dummy';

export default function CategoryAddon() {
  const tableState = useTableState();
  const [data, setData] = React.useState<CategoryAddOnProps[]>(categoryAddOnRecords);
  const table = useDataTableConfig(data, categoryAddOnColumns, tableState);
  const { statusFilter, setStatusFilter } = tableState;

  // Optimized status filter effect with debouncing
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      const statusColumn = table.getColumn('status');
      if (statusColumn) {
        statusColumn.setFilterValue(statusFilter === 'all' ? undefined : statusFilter);
      }
    }, 100); // Small debounce to prevent excessive filtering

    return () => clearTimeout(timeoutId);
  }, [statusFilter, table]);

  return (
    <div className="flex flex-col h-[calc(100vh-88px)] overflow-hidden p-4 pb-0">
      <div className="rounded-md border flex flex-col flex-1 min-h-0">
        <CategoryAddonTableHeader
          table={table}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
        />
        <div className="flex min-h-0 overflow-hidden">
          <DraggableContext setData={setData}>
            <Table className="min-w-full">
              <DataTableHeader isDraggable table={table} />
              <TableBody>
                <SortableContext
                  items={data.map((item) => item.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <SortableRows table={table} columns={categoryAddOnColumns} />
                </SortableContext>
              </TableBody>
            </Table>
          </DraggableContext>
        </div>
      </div>
      <TablePagination table={table} />
    </div>
  );
}
