import * as React from 'react';
import useTableState from '@/hooks/useTableState';
import { categoryData } from '@/constants/data-dummy';
import TablePagination from '@/components/common/table-pagination';
import useDataTableConfig from '@/hooks/useDataTableConfig';
import CategoryTableHeader from '@/components/category/category-table-header';
import DraggableTable from '@/components/common/draggable/draggable-context';
import DataTableHeader from '@/components/data-table/data-table-header';
import { Table, TableBody } from '@/components/ui/table';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableRows from '@/components/data-table/sortable-rows';
import { categoryColumns } from '@/components/data-table/category-columns';

export default function Category() {
  const tableState = useTableState();
  const [data, setData] = React.useState<CategoryProps[]>(categoryData);
  const table = useDataTableConfig(data, categoryColumns, tableState);
  const { statusFilter, setStatusFilter } = tableState;

  // Optimized status filter effect with debouncing
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      const statusColumn = table.getColumn('status');
      if (statusColumn) {
        statusColumn.setFilterValue(statusFilter === 'all' ? undefined : statusFilter);
      }
    }, 100); // Small debounce to prevent excessive filtering

    return () => clearTimeout(timeoutId);
  }, [statusFilter, table]);

  return (
    <div className="flex flex-col h-[calc(100vh-88px)] overflow-hidden p-4 pb-0">
      <div className="rounded-md border flex flex-col flex-1 min-h-0">
        <CategoryTableHeader
          table={table}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
        />
        <div className="flex min-h-0 overflow-hidden">
          <DraggableTable setData={setData}>
            <Table className="min-w-full">
              <DataTableHeader isDraggable table={table} />
              <TableBody>
                <SortableContext
                  items={data.map((item) => item.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <SortableRows table={table} columns={categoryColumns} />
                </SortableContext>
              </TableBody>
            </Table>
          </DraggableTable>
        </div>
      </div>
      <TablePagination table={table} />
    </div>
  );
}
